import { notFound } from 'next/navigation'
import { DynamicPageClient } from './client'

interface PageParams {
  slug: string[]
}

export default async function DynamicPageRoute({ params, searchParams }: { 
  params: Promise<PageParams>, 
  searchParams: Promise<{ [key: string]: string | string[] | undefined }> 
}) {
  // Await params and searchParams
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  
  // Get the full slug path
  const slug = resolvedParams.slug ? resolvedParams.slug.join('/') : ''
  
  // Check for preview mode
  const isPreview = resolvedSearchParams.preview === 'true'
  const version = resolvedSearchParams.version?.toString() || 'latest'
  const locale = resolvedSearchParams.locale?.toString() || 'en'

  // Extract any additional params from search params
  const additionalParams = Object.fromEntries(
    Object.entries(resolvedSearchParams).filter(([key]) => 
      !['preview', 'version', 'locale'].includes(key)
    )
  )

  return (
    <DynamicPageClient
      slug={slug}
      preview={isPreview}
      version={version}
      locale={locale}
      params={additionalParams}
      searchParams={resolvedSearchParams}
    />
  )
}

// Generate metadata for the page
export async function generateMetadata({ params }: { params: Promise<PageParams> }) {
  const resolvedParams = await params
  const slug = resolvedParams.slug ? resolvedParams.slug.join('/') : ''
  
  try {
    // In a real app, fetch page data to generate metadata
    // const page = await fetchPageBySlug(slug)
    
    // Mock metadata for demo
    const mockMetadata = {
      title: 'Dynamic Page',
      description: 'This is a dynamically generated page',
      openGraph: {
        title: 'Dynamic Page',
        description: 'This is a dynamically generated page',
        type: 'website',
      },
    }

    return mockMetadata
  } catch (error) {
    return {
      title: 'Page Not Found',
      description: 'The requested page could not be found',
    }
  }
}

// Generate static params for static generation (optional)
export async function generateStaticParams(): Promise<PageParams[]> {
  // In a real app, fetch all page slugs from your CMS/database
  // const pages = await fetchAllPages()
  // return pages.map(page => ({ slug: page.slug.split('/') }))
  
  // Mock static params for demo
  return [
    { slug: ['welcome'] },
    { slug: ['about'] },
    { slug: ['contact'] },
    { slug: ['blog'] },
    { slug: ['blog', 'getting-started'] },
    { slug: ['products'] },
    { slug: ['products', 'features'] },
  ]
}