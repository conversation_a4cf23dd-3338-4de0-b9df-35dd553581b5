import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import { cache } from 'react'
import { PageRenderer } from '@/components/pages'

interface PageParams {
  slug: string[]
}

// Cache the page fetch function to prevent duplicate requests
const getPageBySlug = cache(async (slug: string, options?: {
  preview?: boolean
  version?: string
  locale?: string
}) => {
  const { preview = false, version, locale = 'en' } = options || {}
  
  try {
    const url = new URL('/api/pages/' + slug, process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000')
    if (preview) url.searchParams.set('preview', 'true')
    if (version) url.searchParams.set('version', version)
    if (locale) url.searchParams.set('locale', locale)

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Use different cache strategies based on preview mode
      next: preview 
        ? { revalidate: 0 } // No cache for preview
        : { revalidate: 300 } // 5 minutes cache for published pages
    })

    if (response.status === 404) {
      return null
    }

    if (!response.ok) {
      throw new Error(`Failed to fetch page: ${response.statusText}`)
    }

    const data = await response.json()
    return data.page || null
  } catch (error) {
    console.error('Error fetching page:', error)
    return null
  }
})

// Cache related pages fetch
const getRelatedPages = cache(async (pageId: string, categoryId?: string, tags?: string[]) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/pages/${pageId}/related`, {
      method: 'GET',
      next: { revalidate: 600 } // 10 minutes cache for related pages
    })

    if (!response.ok) return []
    
    const data = await response.json()
    return data.relatedPages || []
  } catch (error) {
    console.error('Error fetching related pages:', error)
    return []
  }
})

export default async function OptimizedDynamicPageRoute({ 
  params, 
  searchParams 
}: { 
  params: Promise<PageParams>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  
  const slug = resolvedParams.slug ? resolvedParams.slug.join('/') : ''
  const isPreview = resolvedSearchParams.preview === 'true'
  const version = resolvedSearchParams.version?.toString()
  const locale = resolvedSearchParams.locale?.toString() || 'en'

  // Fetch page data server-side
  const page = await getPageBySlug(slug, { preview: isPreview, version, locale })

  if (!page) {
    notFound()
  }

  // Fetch related pages in parallel (only for published pages)
  const relatedPages = !isPreview && page.status === 'PUBLISHED' 
    ? await getRelatedPages(page.id, page.category?.id, page.tags?.map(t => t.id))
    : []

  return (
    <PageRenderer
      page={page}
      relatedPages={relatedPages}
      isPreview={isPreview}
      showComments={!isPreview && page.status === 'PUBLISHED'}
      showRelated={relatedPages.length > 0}
      showTOC={page.template !== 'landing'}
      showBreadcrumbs={true}
      showShare={!isPreview}
      showAuthor={true}
      showMeta={true}
    />
  )
}

// Enhanced metadata generation with caching
export async function generateMetadata({ 
  params 
}: { 
  params: Promise<PageParams> 
}): Promise<Metadata> {
  const resolvedParams = await params
  const slug = resolvedParams.slug ? resolvedParams.slug.join('/') : ''
  
  const page = await getPageBySlug(slug)
  
  if (!page) {
    return {
      title: 'Page Not Found',
      description: 'The requested page could not be found',
    }
  }

  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  const pageUrl = `${baseUrl}/${page.slug}`

  return {
    title: page.seoTitle || page.title,
    description: page.seoDescription || page.excerpt,
    keywords: page.tags?.map(tag => tag.name).join(', '),
    authors: [{ name: page.author.name }],
    creator: page.author.name,
    publisher: page.author.name,
    
    openGraph: {
      title: page.seoTitle || page.title,
      description: page.seoDescription || page.excerpt,
      url: pageUrl,
      siteName: 'Your Site Name',
      images: page.featuredImage ? [
        {
          url: page.featuredImage,
          width: 1200,
          height: 630,
          alt: page.title,
        }
      ] : [],
      locale: 'en_US',
      type: 'article',
      publishedTime: page.publishedAt?.toISOString(),
      modifiedTime: page.updatedAt.toISOString(),
      authors: [page.author.name],
      tags: page.tags?.map(tag => tag.name),
    },
    
    twitter: {
      card: 'summary_large_image',
      title: page.seoTitle || page.title,
      description: page.seoDescription || page.excerpt,
      images: page.featuredImage ? [page.featuredImage] : [],
      creator: '@yourtwitterhandle',
    },
    
    robots: {
      index: page.status === 'PUBLISHED',
      follow: page.status === 'PUBLISHED',
      googleBot: {
        index: page.status === 'PUBLISHED',
        follow: page.status === 'PUBLISHED',
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    
    alternates: {
      canonical: pageUrl,
      languages: {
        'en-US': pageUrl,
        // Add other languages as needed
      },
    },
  }
}

// Enhanced static params generation with ISR
export async function generateStaticParams(): Promise<PageParams[]> {
  try {
    // In production, fetch all published pages
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/pages/sitemap`, {
      method: 'GET',
      next: { revalidate: 3600 } // Revalidate every hour
    })

    if (!response.ok) {
      // Fallback to basic static params
      return [
        { slug: ['welcome'] },
        { slug: ['about'] },
        { slug: ['contact'] },
        { slug: ['blog'] },
        { slug: ['products'] },
      ]
    }

    const data = await response.json()
    return data.pages?.map((page: any) => ({
      slug: page.slug.split('/').filter(Boolean)
    })) || []
  } catch (error) {
    console.error('Error generating static params:', error)
    // Return fallback params
    return [
      { slug: ['welcome'] },
      { slug: ['about'] },
      { slug: ['contact'] },
    ]
  }
}
