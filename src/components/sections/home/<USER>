import Link from "next/link";

export default function Section1() {
    return (
        <>
            {/*=====HERO AREA START =======*/}
            <div className="hero9" style={{ backgroundImage: "url(assets/img/hero/home-hero-bg.jpg)" }}>
                <div className="container">
                    <div className="row align-items-center">
                        <div className="col-lg-6">
                            <div className="main-heading">
                                <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                                    <img src="assets/img/icons/span9.png" alt="" /> Transforming African Infrastructure
                                </span>
                                <h1 className="text-anime-style-3">Innovative Technology Solutions For Smart Cities & Digital Excellence</h1>
                                <div className="space16" />
                                <p>
                                    At Motshwanelo IT Consulting, we specialize in connecting cutting-edge technology with <br /> African infrastructure needs. From Smart Cities to Data Centers.
                                </p>
                                <div className="space30" />
                                <Link className="theme-btn15" href="/service">
                                    Explore Our Solutions
                                    <span>
                                        <i className="fa-solid fa-arrow-right" />
                                    </span>
                                </Link>
                            </div>
                        </div>
                        <div className="col-lg-6">
                            <div className="main-images">
                                <div className="image1" data-aos="zoom-out" data-aos-duration={800}>
                                    <img src="assets/services-imgs/smartcity-1-min.jpg" alt="Smart City Solutions" />
                                </div>
                                <div className="image2" data-aos="flip-right" data-aos-duration={800}>
                                    <img src="assets/services-imgs/datacenter-2-min.jpg" alt="Data Center Solutions" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/*=====HERO AREA END=======*/}

            <style jsx>{`
                .hero9 {
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                    position: relative;
                    padding: 120px 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                }

                .hero9::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(
                        135deg,
                        rgba(3, 39, 110, 0.8) 0%,
                        rgba(0, 123, 255, 0.6) 50%,
                        rgba(0, 0, 0, 0.4) 100%
                    );
                    z-index: 1;
                }

                .hero9 .container {
                    position: relative;
                    z-index: 2;
                }

                .hero9 .main-heading h1 {
                    color: white;
                    font-size: 3.5rem;
                    font-weight: 700;
                    line-height: 1.2;
                    margin-bottom: 1rem;
                }

                .hero9 .main-heading p {
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 1.1rem;
                    line-height: 1.6;
                    margin-bottom: 2rem;
                }

                .hero9 .span {
                    color: #00c6ff;
                    font-weight: 600;
                    font-size: 1rem;
                    display: flex;
                    align-items: center;
                    margin-bottom: 1rem;
                }

                .hero9 .span img {
                    margin-right: 10px;
                    width: 20px;
                    height: 20px;
                }

                .main-images {
                    position: relative;
                    height: 500px;
                }

                .main-images .image1 {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 60%;
                    height: 70%;
                    border-radius: 20px;
                    overflow: hidden;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    z-index: 2;
                }

                .main-images .image2 {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    width: 60%;
                    height: 70%;
                    border-radius: 20px;
                    overflow: hidden;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    z-index: 1;
                }

                .main-images img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transition: transform 0.3s ease;
                }

                .main-images img:hover {
                    transform: scale(1.05);
                }

                @media (max-width: 768px) {
                    .hero9 {
                        padding: 80px 0;
                        min-height: 80vh;
                    }

                    .hero9 .main-heading h1 {
                        font-size: 2.5rem;
                    }

                    .main-images {
                        height: 400px;
                        margin-top: 3rem;
                    }

                    .main-images .image1,
                    .main-images .image2 {
                        width: 70%;
                        height: 60%;
                    }
                }
            `}</style>
        </>
    );
}
